import base64
# pip install pycryptodome
from Crypto.Cipher import AES
STATE = [[0]*4 for _ in range(4)]
ROUNDKEY = [0]*176

# KEY = None
# IV = None

# The number of columns comprising a state in AES. This is a constant in AES. Value=4
Nb = 4
# The number of 32 bit words in a key.
Nk = 4
# Key length in bytes [128 bit]
KEYLEN = 16
# The number of rounds in AES Cipher.
Nr = 10

sbox = [
    # 0     1    2      3     4    5     6     7      8    9     A      B    C     D     E     F
    0x63, 0x7c, 0x77, 0x7b, 0xf2, 0x6b, 0x6f, 0xc5, 0x30, 0x01, 0x67, 0x2b, 0xfe, 0xd7, 0xab, 0x76,
    0xca, 0x82, 0xc9, 0x7d, 0xfa, 0x59, 0x47, 0xf0, 0xad, 0xd4, 0xa2, 0xaf, 0x9c, 0xa4, 0x72, 0xc0,
    0xb7, 0xfd, 0x93, 0x26, 0x36, 0x3f, 0xf7, 0xcc, 0x34, 0xa5, 0xe5, 0xf1, 0x71, 0xd8, 0x31, 0x15,
    0x04, 0xc7, 0x23, 0xc3, 0x18, 0x96, 0x05, 0x9a, 0x07, 0x12, 0x80, 0xe2, 0xeb, 0x27, 0xb2, 0x75,
    0x09, 0x83, 0x2c, 0x1a, 0x1b, 0x6e, 0x5a, 0xa0, 0x52, 0x3b, 0xd6, 0xb3, 0x29, 0xe3, 0x2f, 0x84,
    0x53, 0xd1, 0x00, 0xed, 0x20, 0xfc, 0xb1, 0x5b, 0x6a, 0xcb, 0xbe, 0x39, 0x4a, 0x4c, 0x58, 0xcf,
    0xd0, 0xef, 0xaa, 0xfb, 0x43, 0x4d, 0x33, 0x85, 0x45, 0xf9, 0x02, 0x7f, 0x50, 0x3c, 0x9f, 0xa8,
    0x51, 0xa3, 0x40, 0x8f, 0x92, 0x9d, 0x38, 0xf5, 0xbc, 0xb6, 0xda, 0x21, 0x10, 0xff, 0xf3, 0xd2,
    0xcd, 0x0c, 0x13, 0xec, 0x5f, 0x97, 0x44, 0x17, 0xc4, 0xa7, 0x7e, 0x3d, 0x64, 0x5d, 0x19, 0x73,
    0x60, 0x81, 0x4f, 0xdc, 0x22, 0x2a, 0x90, 0x88, 0x46, 0xee, 0xb8, 0x14, 0xde, 0x5e, 0x0b, 0xdb,
    0xe0, 0x32, 0x3a, 0x0a, 0x49, 0x06, 0x24, 0x5c, 0xc2, 0xd3, 0xac, 0x62, 0x91, 0x95, 0xe4, 0x79,
    0xe7, 0xc8, 0x37, 0x6d, 0x8d, 0xd5, 0x4e, 0xa9, 0x6c, 0x56, 0xf4, 0xea, 0x65, 0x7a, 0xae, 0x08,
    0xba, 0x78, 0x25, 0x2e, 0x1c, 0xa6, 0xb4, 0xc6, 0xe8, 0xdd, 0x74, 0x1f, 0x4b, 0xbd, 0x8b, 0x8a,
    0x70, 0x3e, 0xb5, 0x66, 0x48, 0x03, 0xf6, 0x0e, 0x61, 0x35, 0x57, 0xb9, 0x86, 0xc1, 0x1d, 0x9e,
    0xe1, 0xf8, 0x98, 0x11, 0x69, 0xd9, 0x8e, 0x94, 0x9b, 0x1e, 0x87, 0xe9, 0xce, 0x55, 0x28, 0xdf,
    0x8c, 0xa1, 0x89, 0x0d, 0xbf, 0xe6, 0x42, 0x68, 0x41, 0x99, 0x2d, 0x0f, 0xb0, 0x54, 0xbb, 0x16]

rsbox = [
    0x52, 0x09, 0x6a, 0xd5, 0x30, 0x36, 0xa5, 0x38, 0xbf, 0x40, 0xa3, 0x9e, 0x81, 0xf3, 0xd7, 0xfb,
    0x7c, 0xe3, 0x39, 0x82, 0x9b, 0x2f, 0xff, 0x87, 0x34, 0x8e, 0x43, 0x44, 0xc4, 0xde, 0xe9, 0xcb,
    0x54, 0x7b, 0x94, 0x32, 0xa6, 0xc2, 0x23, 0x3d, 0xee, 0x4c, 0x95, 0x0b, 0x42, 0xfa, 0xc3, 0x4e,
    0x08, 0x2e, 0xa1, 0x66, 0x28, 0xd9, 0x24, 0xb2, 0x76, 0x5b, 0xa2, 0x49, 0x6d, 0x8b, 0xd1, 0x25,
    0x72, 0xf8, 0xf6, 0x64, 0x86, 0x68, 0x98, 0x16, 0xd4, 0xa4, 0x5c, 0xcc, 0x5d, 0x65, 0xb6, 0x92,
    0x6c, 0x70, 0x48, 0x50, 0xfd, 0xed, 0xb9, 0xda, 0x5e, 0x15, 0x46, 0x57, 0xa7, 0x8d, 0x9d, 0x84,
    0x90, 0xd8, 0xab, 0x00, 0x8c, 0xbc, 0xd3, 0x0a, 0xf7, 0xe4, 0x58, 0x05, 0xb8, 0xb3, 0x45, 0x06,
    0xd0, 0x2c, 0x1e, 0x8f, 0xca, 0x3f, 0x0f, 0x02, 0xc1, 0xaf, 0xbd, 0x03, 0x01, 0x13, 0x8a, 0x6b,
    0x3a, 0x91, 0x11, 0x41, 0x4f, 0x67, 0xdc, 0xea, 0x97, 0xf2, 0xcf, 0xce, 0xf0, 0xb4, 0xe6, 0x73,
    0x96, 0xac, 0x74, 0x22, 0xe7, 0xad, 0x35, 0x85, 0xe2, 0xf9, 0x37, 0xe8, 0x1c, 0x75, 0xdf, 0x6e,
    0x47, 0xf1, 0x1a, 0x71, 0x1d, 0x29, 0xc5, 0x89, 0x6f, 0xb7, 0x62, 0x0e, 0xaa, 0x18, 0xbe, 0x1b,
    0xfc, 0x56, 0x3e, 0x4b, 0xc6, 0xd2, 0x79, 0x20, 0x9a, 0xdb, 0xc0, 0xfe, 0x78, 0xcd, 0x5a, 0xf4,
    0x1f, 0xdd, 0xa8, 0x33, 0x88, 0x07, 0xc7, 0x31, 0xb1, 0x12, 0x10, 0x59, 0x27, 0x80, 0xec, 0x5f,
    0x60, 0x51, 0x7f, 0xa9, 0x19, 0xb5, 0x4a, 0x0d, 0x2d, 0xe5, 0x7a, 0x9f, 0x93, 0xc9, 0x9c, 0xef,
    0xa0, 0xe0, 0x3b, 0x4d, 0xae, 0x2a, 0xf5, 0xb0, 0xc8, 0xeb, 0xbb, 0x3c, 0x83, 0x53, 0x99, 0x61,
    0x17, 0x2b, 0x04, 0x7e, 0xba, 0x77, 0xd6, 0x26, 0xe1, 0x69, 0x14, 0x63, 0x55, 0x21, 0x0c, 0x7d]


""" The round constant word array, Rcon[i], contains the values given by
    x to th e power (i-1) being powers of x (x is denoted as {02}) in the field GF(2^8)
    Note that i starts at 1, not 0)."""
Rcon = [
    0x8d, 0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0x1b, 0x36, 0x6c, 0xd8, 0xab, 0x4d, 0x9a,
    0x2f, 0x5e, 0xbc, 0x63, 0xc6, 0x97, 0x35, 0x6a, 0xd4, 0xb3, 0x7d, 0xfa, 0xef, 0xc5, 0x91, 0x39,
    0x72, 0xe4, 0xd3, 0xbd, 0x61, 0xc2, 0x9f, 0x25, 0x4a, 0x94, 0x33, 0x66, 0xcc, 0x83, 0x1d, 0x3a,
    0x74, 0xe8, 0xcb, 0x8d, 0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0x1b, 0x36, 0x6c, 0xd8,
    0xab, 0x4d, 0x9a, 0x2f, 0x5e, 0xbc, 0x63, 0xc6, 0x97, 0x35, 0x6a, 0xd4, 0xb3, 0x7d, 0xfa, 0xef,
    0xc5, 0x91, 0x39, 0x72, 0xe4, 0xd3, 0xbd, 0x61, 0xc2, 0x9f, 0x25, 0x4a, 0x94, 0x33, 0x66, 0xcc,
    0x83, 0x1d, 0x3a, 0x74, 0xe8, 0xcb, 0x8d, 0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0x1b,
    0x36, 0x6c, 0xd8, 0xab, 0x4d, 0x9a, 0x2f, 0x5e, 0xbc, 0x63, 0xc6, 0x97, 0x35, 0x6a, 0xd4, 0xb3,
    0x7d, 0xfa, 0xef, 0xc5, 0x91, 0x39, 0x72, 0xe4, 0xd3, 0xbd, 0x61, 0xc2, 0x9f, 0x25, 0x4a, 0x94,
    0x33, 0x66, 0xcc, 0x83, 0x1d, 0x3a, 0x74, 0xe8, 0xcb, 0x8d, 0x01, 0x02, 0x04, 0x08, 0x10, 0x20,
    0x40, 0x80, 0x1b, 0x36, 0x6c, 0xd8, 0xab, 0x4d, 0x9a, 0x2f, 0x5e, 0xbc, 0x63, 0xc6, 0x97, 0x35,
    0x6a, 0xd4, 0xb3, 0x7d, 0xfa, 0xef, 0xc5, 0x91, 0x39, 0x72, 0xe4, 0xd3, 0xbd, 0x61, 0xc2, 0x9f,
    0x25, 0x4a, 0x94, 0x33, 0x66, 0xcc, 0x83, 0x1d, 0x3a, 0x74, 0xe8, 0xcb, 0x8d, 0x01, 0x02, 0x04,
    0x08, 0x10, 0x20, 0x40, 0x80, 0x1b, 0x36, 0x6c, 0xd8, 0xab, 0x4d, 0x9a, 0x2f, 0x5e, 0xbc, 0x63,
    0xc6, 0x97, 0x35, 0x6a, 0xd4, 0xb3, 0x7d, 0xfa, 0xef, 0xc5, 0x91, 0x39, 0x72, 0xe4, 0xd3, 0xbd,
    0x61, 0xc2, 0x9f, 0x25, 0x4a, 0x94, 0x33, 0x66, 0xcc, 0x83, 0x1d, 0x3a, 0x74, 0xe8, 0xcb]


def getSBoxValue(num):
    return sbox[num]


def getSBoxInvert(num):
    return rsbox[num]


def KeyExpansion(key):
    tempa = [0, 0, 0, 0]  # Used for the column/row operations

    # The first round key is the key itself.
    for i in range(0, Nk):
        ROUNDKEY[(i * 4) + 0] = key[(i * 4) + 0]
        ROUNDKEY[(i * 4) + 1] = key[(i * 4) + 1]
        ROUNDKEY[(i * 4) + 2] = key[(i * 4) + 2]
        ROUNDKEY[(i * 4) + 3] = key[(i * 4) + 3]

    i = i+1
    # All other round keys are found from the previous round keys.
    for i in range(i, (Nb * (Nr + 1))):
        for j in range(0, 4):
            tempa[j] = ROUNDKEY[(i-1) * 4 + j]
        if i % Nk == 0:
            # This function rotates the 4 bytes in a word to the left once.
            # [a0,a1,a2,a3] becomes [a1,a2,a3,a0]
            # Function RotWord()
            k = tempa[0]
            tempa[0] = tempa[1]
            tempa[1] = tempa[2]
            tempa[2] = tempa[3]
            tempa[3] = k

            # SubWord() is a function that takes a four-byte input word and
            # applies the S-box to each of the four bytes to produce an output word.

            # Function Subword()
            tempa[0] = getSBoxValue(tempa[0])
            tempa[1] = getSBoxValue(tempa[1])
            tempa[2] = getSBoxValue(tempa[2])
            tempa[3] = getSBoxValue(tempa[3])

            tempa[0] = tempa[0] ^ Rcon[i//Nk]

        else:
            if (Nk > 6 and i % Nk == 4):
                # Function Subword()
                tempa[0] = getSBoxValue(tempa[0])
                tempa[1] = getSBoxValue(tempa[1])
                tempa[2] = getSBoxValue(tempa[2])
                tempa[3] = getSBoxValue(tempa[3])

        ROUNDKEY[i * 4 + 0] = ROUNDKEY[(i - Nk) * 4 + 0] ^ tempa[0]
        ROUNDKEY[i * 4 + 1] = ROUNDKEY[(i - Nk) * 4 + 1] ^ tempa[1]
        ROUNDKEY[i * 4 + 2] = ROUNDKEY[(i - Nk) * 4 + 2] ^ tempa[2]
        ROUNDKEY[i * 4 + 3] = ROUNDKEY[(i - Nk) * 4 + 3] ^ tempa[3]

# This function adds the round key to state.
# The round key is added to the state by an XOR function.


def AddRoundKey(round):
    for i in range(0, 4):
        for j in range(0, 4):
            STATE[i][j] ^= ROUNDKEY[round * Nb * 4 + i * Nb + j]


# The SubBytes Function Substitutes the values in the
# state matrix with values in an S-box.
def SubBytes():
    for i in range(0, 4):
        for j in range(0, 4):
            STATE[j][i] = getSBoxValue(STATE[j][i])


# The ShiftRows() function shifts the rows in the state to the left.
# Each row is shifted with different offset.
# Offset = Row number. So the first row is not shifted.
def ShiftRows():
    # Rotate first row 1 columns to left
    temp = STATE[0][1]
    STATE[0][1] = STATE[1][1]
    STATE[1][1] = STATE[2][1]
    STATE[2][1] = STATE[3][1]
    STATE[3][1] = temp

    # Rotate second row 2 columns to left
    temp = STATE[0][2]
    STATE[0][2] = STATE[2][2]
    STATE[2][2] = temp

    temp = STATE[1][2]
    STATE[1][2] = STATE[3][2]
    STATE[3][2] = temp

    # Rotate third row 3 columns to left
    temp = STATE[0][3]
    STATE[0][3] = STATE[3][3]
    STATE[3][3] = STATE[2][3]
    STATE[2][3] = STATE[1][3]
    STATE[1][3] = temp


def xtime(x):
    return (((x << 1) ^ (((x >> 7) & 1) * 0x1b)) % 256)

# MixColumns function mixes the columns of the state matrix


def MixColumns():
    for i in range(0, 4):
        t = STATE[i][0]
        Tmp = STATE[i][0] ^ STATE[i][1] ^ STATE[i][2] ^ STATE[i][3]
        Tm = STATE[i][0] ^ STATE[i][1]
        Tm = xtime(Tm)
        STATE[i][0] ^= Tm ^ Tmp
        Tm = STATE[i][1] ^ STATE[i][2]
        Tm = xtime(Tm)
        STATE[i][1] ^= Tm ^ Tmp
        Tm = STATE[i][2] ^ STATE[i][3]
        Tm = xtime(Tm)
        STATE[i][2] ^= Tm ^ Tmp
        Tm = STATE[i][3] ^ t
        Tm = xtime(Tm)
        STATE[i][3] ^= Tm ^ Tmp

# Multiply is used to multiply numbers in the field GF(2^8)


def Multiply(x, y):
    return (((y & 1) * x) ^
            ((y >> 1 & 1) * xtime(x)) ^
            ((y >> 2 & 1) * xtime(xtime(x))) ^
            ((y >> 3 & 1) * xtime(xtime(xtime(x)))) ^
            ((y >> 4 & 1) * xtime(xtime(xtime(xtime(x))))))

# MixColumns function mixes the columns of the state matrix.
# The method used to multiply may be difficult to understand for the inexperienced.
# Please use the references to gain more information.


def InvMixColumns():
    for i in range(0, 4):
        a = STATE[i][0]
        b = STATE[i][1]
        c = STATE[i][2]
        d = STATE[i][3]

        STATE[i][0] = Multiply(a, 0x0e) ^ Multiply(
            b, 0x0b) ^ Multiply(c, 0x0d) ^ Multiply(d, 0x09)
        STATE[i][1] = Multiply(a, 0x09) ^ Multiply(
            b, 0x0e) ^ Multiply(c, 0x0b) ^ Multiply(d, 0x0d)
        STATE[i][2] = Multiply(a, 0x0d) ^ Multiply(
            b, 0x09) ^ Multiply(c, 0x0e) ^ Multiply(d, 0x0b)
        STATE[i][3] = Multiply(a, 0x0b) ^ Multiply(
            b, 0x0d) ^ Multiply(c, 0x09) ^ Multiply(d, 0x0e)


# The SubBytes Function Substitutes the values in the
# state matrix with values in an S-box.
def InvSubBytes():
    for i in range(0, 4):
        for j in range(0, 4):
            try:
                STATE[j][i] = getSBoxInvert(STATE[j][i])
            except:
                pass


def InvShiftRows():
    # Rotate first row 1 columns to right
    temp = STATE[3][1]
    STATE[3][1] = STATE[2][1]
    STATE[2][1] = STATE[1][1]
    STATE[1][1] = STATE[0][1]
    STATE[0][1] = temp

    # Rotate second row 2 columns to right
    temp = STATE[0][2]
    STATE[0][2] = STATE[2][2]
    STATE[2][2] = temp

    temp = STATE[1][2]
    STATE[1][2] = STATE[3][2]
    STATE[3][2] = temp

    # Rotate third row 3 columns to right
    temp = STATE[0][3]
    STATE[0][3] = STATE[1][3]
    STATE[1][3] = STATE[2][3]
    STATE[2][3] = STATE[3][3]
    STATE[3][3] = temp

# Cipher is the main function that encrypts the PlainText.


def LoadStateArray(input_bs: bytes):
    for i in range(0, 4):
        for j in range(0, 4):
            STATE[i][j] = input_bs[4*i + j]


def StoreStateArray() -> bytes:
    out = bytearray()
    for i in range(0, 4):
        for j in range(0, 4):
            out += bytes([STATE[i][j]])
    return bytes(out)





def Cipher(block: bytes) -> bytes:
    """
    main_EncryptVortexMsg
    """
    LoadStateArray(block)
    # Add the First round key to the state before starting the rounds.
    AddRoundKey(0)

    # There will be Nr rounds.
    # The first Nr-1 rounds are identical.
    # These Nr-1 rounds are executed in the loop below.
    for round in range(1, Nr):
        SubBytes()
        ShiftRows()
        MixColumns()
        AddRoundKey(round)

    # The last round is given below.
    # The MixColumns function is not here in the last round.
    SubBytes()
    ShiftRows()
    AddRoundKey(Nr)
    ret = StoreStateArray()
    return ret



def XXCipher(block: bytes) -> bytes:
    """
    main_encryptmsg
    """
    LoadStateArray(block)
    AddRoundKey(0)
    InvMixColumns()

    for round in range(1, Nr):
        SubBytes()
        ShiftRows()
        MixColumns()
        InvShiftRows()
        AddRoundKey(round)
        ShiftRows()

    SubBytes()
    ShiftRows()
    AddRoundKey(Nr)
    ret = StoreStateArray()
    return ret





def InvCipher(block: bytes) -> bytes:
    """
    main_DecryptVortexMsg
    """
    # Add the First round key to the state before starting the rounds.
    LoadStateArray(block)
    AddRoundKey(Nr)

    # There will be Nr rounds.
    # The first Nr-1 rounds are identical.
    # These Nr-1 rounds are executed in the loop below.
    for round in range(Nr-1, 0, -1):
        InvShiftRows()
        InvSubBytes()
        AddRoundKey(round)
        InvMixColumns()

    # The last round is given below.
    # The MixColumns function is not here in the last round.
    InvShiftRows()
    InvSubBytes()
    AddRoundKey(0)
    ret = StoreStateArray()
    return ret





def XXInvCipher(block: bytes) -> bytes:
    """
    main_decryptmsg
    """
    LoadStateArray(block)
    AddRoundKey(Nr)
    InvShiftRows()
    InvSubBytes()

    for round in range(Nr-1, 0, -1):
        InvShiftRows()
        AddRoundKey(round)
        ShiftRows()
        InvMixColumns()
        InvShiftRows()
        InvSubBytes()
    MixColumns()
    AddRoundKey(0)
    ret = StoreStateArray()
    return ret


def XXAES128_ECB_encrypt(input_data: bytes, key: bytes) -> bytes:
    # do main_encryptmsg

    # Copy input to output, and work in-memory on output
    out = b''
    data_len = len(input_data)
    if (data_len % 16) != 0:
        print('[!]data need pad!')

    elif len(key) != 16:
        print('[!]key error!')
    else:
        KeyExpansion(key)
        for i in range(0, data_len, 0x10):
            # The next function call encrypts the PlainText with the Key using AES algorithm.
            out += XXCipher(input_data[i:i+0x10])
    return out


def XXAES128_ECB_decrypt(input_data: bytes, key: bytes) -> bytes:
    # do main_decryptmsg
    out = b''
    data_len = len(input_data)
    if (data_len % 16) != 0:
        print('[!]data need pad!')
    elif len(key) != 16:
        print('[!]key error!')
    else:
        # The KeyExpansion routine must be called before encryption.
        KeyExpansion(key)
        for i in range(0, data_len, 0x10):
            # The next function call encrypts the PlainText with the Key using AES algorithm.
            out += XXInvCipher(input_data[i:i+0x10])
    return out


def do_test():
    ciphertext = b'U/KQCMzK/Y8gFkjaK0XX0EQCQIICcPGi4mhFv2EEn+oNkCf/7mLaSF7ppKcEt1Q30T34xwdX7Ngy/UlRxSNOMg=='*4*2
    plaintext = base64.b64decode(ciphertext)
    key = b'qyovE1Jb4atlq58F'

    decrypted = XXAES128_ECB_decrypt(plaintext, key)

    print("Decrypted:", decrypted)

    pass

def generate_license_encdata():
    # 1. 构造 license_info
    license_info = "01-01-2022:01-08-2099:brute:<EMAIL>:brute ratel"
    license_info_bytes = license_info.encode("utf-8")

    # 自动补全到 16 字节对齐
    padded_data = pad_zero(license_info_bytes, block_size=16)

    # 2. 魔改 AES 加密（你需要提供 XXAES128_ECB_encrypt 实现）
    key = b'qyovE1Jb4atlq58F'
    enc1 = XXAES128_ECB_encrypt(padded_data, key)

    # 3. 标准 AES-ECB 加密
    enc2 = AES_ECB_128_ENC(enc1, key)

    # 4. Base64 编码
    b64_encoded = base64.b64encode(enc2).decode()

    # 5. 拼接 license_encdata
    license_encdata = key.decode() + ":" + b64_encoded
    print("Fake license_encdata:")
    print(license_encdata)

def AES_ECB_128_ENC(plaintext: bytes, key: bytes) -> bytes:
    """
    cryptodome AES 标准算法
    do main_EncryptVortexMsg
    """
    aes_cipher = AES.new(key=key, mode=AES.MODE_ECB)
    encrypted = aes_cipher.encrypt(plaintext)
    return encrypted


def AES_ECB_128_DEC(encrypted: bytes, key: bytes) -> bytes:
    """
    cryptodome AES 标准算法
    do main_DecryptVortexMsg
    """
    aes_cipher = AES.new(key=key, mode=AES.MODE_ECB)
    decrypted = aes_cipher.decrypt(encrypted)
    return decrypted

def pad_zero(data: bytes, block_size: int = 16) -> bytes:
    padding_length = (block_size - (len(data) % block_size)) % block_size
    return data + b'\x00' * padding_length


main_DecryptVortexMsg = AES_ECB_128_DEC
main_decryptmsg = XXAES128_ECB_decrypt
main_EncryptVortexMsg = AES_ECB_128_ENC
main_encryptmsg = XXAES128_ECB_encrypt




if __name__ == "__main__":
    generate_license_encdata()
    do_test()
    pass

